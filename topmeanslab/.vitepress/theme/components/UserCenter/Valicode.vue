<template>
    <div class="captcha" style="display: flex;">
        <canvas ref="canvas" width="100" height="40"></canvas>
        <div class="valicode-btn">
        <el-button type="text" class="link-button" @click="refresh">看不清，换一张</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 定义自定义事件，用于向父组件传递验证码
const emits = defineEmits(['getCode']);

// 引用 canvas 和绘图上下文
const canvasRef = ref<HTMLCanvasElement | null>(null);
const ctx = ref<CanvasRenderingContext2D | null>(null);

// 验证码相关变量
const code = ref('');
const chars = 'ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijmnopqrstuvwxyz023456789';
const charLength = chars.length;

// 随机生成一个字符
function getRandomChar() {
  return chars.charAt(Math.floor(Math.random() * charLength));
}

// 绘制验证码
function draw() {
  if (!ctx.value) return;

  // 清空画布
  ctx.value.clearRect(0, 0, canvasRef.value!.width, canvasRef.value!.height);

  // 绘制验证码字符
  let x = 10;
  code.value = ''; // 清空之前的验证码
  for (let i = 0; i < 4; i++) {
    const c = getRandomChar();
    code.value += c;
    ctx.value.font = 'bold 20px Arial';
    ctx.value.fillStyle = '#333';
    ctx.value.fillText(c, x, 25);
    x += 20;
  }

  // 绘制干扰线
  for (let i = 0; i < 10; i++) {
    ctx.value.strokeStyle = '#ccc';
    ctx.value.beginPath();
    ctx.value.moveTo(Math.random() * 100, Math.random() * 40);
    ctx.value.lineTo(Math.random() * 100, Math.random() * 40);
    ctx.value.stroke();
  }

  // 将生成的验证码传递给父组件
  emits('getCode', code.value);
}

// 刷新验证码
function refresh() {
  draw();
}

defineExpose({
  refresh
});

// 组件挂载时初始化
onMounted(() => {
  canvasRef.value = document.querySelector('canvas');
  ctx.value = canvasRef.value?.getContext('2d');
  draw();
});
</script>

<style scoped>
    .captcha {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    gap: 10px;
    }

    .valicode-btn {
    height: 40px;
    line-height: 40px;
    }
</style>