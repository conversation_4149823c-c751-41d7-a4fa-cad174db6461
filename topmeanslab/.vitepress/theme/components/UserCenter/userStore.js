import { defineStore } from 'pinia'
import axios from 'axios'
import log from 'loglevel'

const API_BASE = `${import.meta.env.VITE_BACKEND_SRV_URL}/api`;
log.setLevel('info');

export const useUserStore = defineStore('user', {
  state: () => ({
    rememberPassword: false,
    passwordRememberDays: 7,
    autoLoginDays: 1,
    isLoading: false,
    error: null,
    storedPassword: typeof window !== 'undefined' ? localStorage.getItem('storedPassword') || null : null,
    passwordExpire: typeof window !== 'undefined' ? localStorage.getItem('passwordExpire') || null : null,

    userInfo: typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('userInfo')) || { avatar: '/images/default-avatar.jpg' }
      : { avatar: '/images/default-avatar.jpg' },
    token: typeof window !== 'undefined' ? localStorage.getItem('userToken') || null : null,
    lastLoginTime: typeof window !== 'undefined' ? localStorage.getItem('lastLoginTime') || null : null
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && !state.isLoginExpired,
    isLoginExpired: (state) => {
      if (!state.lastLoginTime) return true;
      const now = new Date().getTime();
      const lastLogin = new Date(state.lastLoginTime).getTime();
      return now - lastLogin > 1 * 24 * 60 * 60 * 1000;
    }
  },

  actions: {
    setUserInfo(info) {
      this.userInfo = info || {
        avatar: '',
        nickname: '',
        signature: '...',
      };
    },

    setToken(token) {
      this.token = token;
      this.lastLoginTime = new Date().toISOString();
      if (this.rememberPassword) {
        if (typeof window !== 'undefined') {
          localStorage.setItem('userToken', token);
          localStorage.setItem('lastLoginTime', this.lastLoginTime);
        }
      } else {
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('userToken', token);
          sessionStorage.setItem('lastLoginTime', this.lastLoginTime);
        }
      }
    },

    setStoredPassword(password) {
      if (typeof window !== 'undefined') {
        const expire = new Date().getTime() + 7 * 24 * 60 * 60 * 1000;
        localStorage.setItem('storedPassword', password);
        localStorage.setItem('passwordExpire', expire);
        this.storedPassword = password;
        this.passwordExpire = expire;
      }
    },

    clearStoredPassword() {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('storedPassword');
        localStorage.removeItem('passwordExpire');
      }
      this.storedPassword = null;
      this.passwordExpire = null;
    },

    checkLoginStatus() {
      if (typeof window !== 'undefined') {
        this.token = localStorage.getItem('userToken') || sessionStorage.getItem('userToken');
        this.lastLoginTime = localStorage.getItem('lastLoginTime') || sessionStorage.getItem('lastLoginTime');
      }

      if (this.token && this.lastLoginTime) {
        const isExpired = new Date().getTime() - new Date(this.lastLoginTime).getTime() > 1 * 24 * 60 * 60 * 1000;
        return !isExpired;
      }
      return false;
    },

    async initAuthState() {
      try {
        if (typeof window !== 'undefined') {
          this.token = localStorage.getItem('userToken') || sessionStorage.getItem('userToken');
          this.lastLoginTime = localStorage.getItem('lastLoginTime') || sessionStorage.getItem('lastLoginTime');
          this.userInfo = JSON.parse(localStorage.getItem('userInfo')) || {
            avatar: '/images/default-avatar.jpg',
            nickname: '游客',
            signature: '点击登录查看个人信息'
          };
        }

        if (this.checkLoginStatus()) {
          await this.getUserInfo();
        }
      } catch (error) {
        log.error('初始化失败:', error);
      }
    },

    // 获取用户信息的函数
    async getUserInfo() {
      try {
        this.isLoading = true

        const response = await fetch(`${API_BASE}/user/info`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          this.setUserInfo({
            ...data.user,
            // 添加默认值保护
            avatar: data.user.avatar || '/images/default-avatar.jpg'
          })
          localStorage.setItem('userInfo', JSON.stringify(data.user))
        }
        return data
      } catch (error) {
        log.error('获取用户信息失败:', error);
        return {
          success: false,
          message: error.data?.message || '获取用户信息失败'
        }
      }
    },

    // 更新用户资料
    async updateProfile(profileData) {
      try {
        const payload = {
          ...profileData,
          userId: this.userInfo.id // 确保包含用户ID
        };

        const response = await axios.put(`${API_BASE}/user/profile`, payload, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          }
        });

        if (response.data.success) {
          this.setUserInfo({
            ...this.userInfo,
            ...profileData
          });
        }
        return response.data;
      } catch (error) {
        log.error('更新资料失败:', error);
        return {
          success: false,
          message: error.response?.data?.message || '更新失败'
        };
      }
    },


    // 更新用户头像
    async updateAvatar(file) {
      try {
        this.isLoading = true;

        log.info('store接收到的文件:', {
          isFile: file instanceof File,
          name: file?.name,
          type: file?.type,
          size: file?.size,
          proto: Object.getPrototypeOf(file),
          methods: Object.getOwnPropertyNames(file)
        });

        const formData = new FormData();
        formData.append('avatarFile', file);
        formData.append('userId', this.userInfo.id);
        const response = await axios.put(`${API_BASE}/user/avatar`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        if (response.data.success) {
          this.setUserInfo({
            ...this.userInfo,
            avatar: response.data.avatarUrl
          });
        }
        return response.data;
      } catch (error) {
        log.error('更新头像失败:', error);
        this.error = error.response?.data?.message || '更新头像失败';
        return { success: false, message: this.error };
      } finally {
        this.isLoading = false;
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        this.isLoading = true

        // 添加用户ID获取逻辑
        const userId = this.userInfo?.id

        const response = await axios.put(`${API_BASE}/user/password`, {
          userId: this.userInfo?.id,
          oldPassword: passwordData.currentPassword, // 保持字段名对齐后端
          newPassword: passwordData.newPassword       // 保持明文传输
        }, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        })

        return response.data
      } catch (error) {
        log.error('修改密码失败:', error);
        this.error = error.response?.data?.message || '修改密码失败'
        return { success: false, message: this.error }
      } finally {
        this.isLoading = false
      }
    },

    logout() {
      // 清除所有存储介质
      this.userInfo = null;
      this.token = null;
      this.lastLoginTime = null;

      // 清除本地存储
      localStorage.removeItem('userToken');
      localStorage.removeItem('lastLoginTime');

      // 清除会话存储
      sessionStorage.removeItem('userToken');
      sessionStorage.removeItem('lastLoginTime');

      // 清除自动登录相关
      this.clearStoredPassword();

      // 强制刷新路由
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    },

    toggleShow(show) {
      if (!show) {
        this.logout()
      } else {
        log.error('不能直接设置登录状态, 请使用setToken方法');
      }
    }
  }
})
