<template>
  <div class="user-profile">
    <!-- 未登录状态提示 -->
    <div v-if="!userStore.isLoggedIn" class="login-prompt">
      <el-alert
        title="请先登录账号"
        type="warning"
        :closable="false"
        :show-icon="false"
      >
        <template #default>
          <el-button type="primary" @click="goToLogin">去登录</el-button>
        </template>
      </el-alert>
    </div>

    <!-- 已登录状态显示内容 -->
    <template v-else>
      <!-- 顶部个人信息卡片 -->
      <div class="profile-card">
        <div class="profile-header">
          <div class="profile-cover">
            <div class="cover-overlay"></div>
          </div>
          <div class="profile-info">
            <div class="avatar-wrapper">
              <img
                :src="userStore.userInfo.avatar || '/images/default-avatar.jpg'"
                :alt="userStore.userInfo.nickname"
                class="avatar"
              >
              <!-- <div class="avatar-actions">
                <el-upload
                  action="#"
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="handleAvatarChange"
                  accept="image/jpeg, image/jpg, image/png, image/gif, image/webp"
                >
                  <button class="upload-btn">
                    <el-icon><Camera /></el-icon>
                    更换头像
                  </button>
                </el-upload>
              </div> -->
            </div>
            <div class="user-details">
              <div class="user-name">
                <div class="editable-field" style="position: relative; display: flex; align-items: center;">
                  <el-input
                    v-if="isEditing.nickname"
                    ref="nicknameInput"
                    v-model="editForm.nickname"
                    size="small"
                    style="width: 300px;"
                    @blur="handleFieldBlur('nickname')"
                  />
                  <h1
                    v-else
                    style="flex: 1; cursor: pointer; margin: 0; white-space: nowrap;"
                    @click="startEditing('nickname')"
                  >
                    {{ userStore.userInfo.nickname || '未设置昵称' }}
                  </h1>
                  <el-icon
                    v-show="!isEditing.nickname"
                    class="edit-icon"
                    style="margin-left: 12px; color: #666;"
                    @click="startEditing('nickname')"
                  >
                    <Edit />
                  </el-icon>
                </div>
              </div>
              <div class="user-signature">
                <div class="editable-field" style="position: relative; display: flex; align-items: center;">
                  <el-input
                    v-if="isEditing.signature"
                    ref="signatureInput"
                    v-model="editForm.signature"
                    size="small"
                    style="width: 300px;"
                    @blur="handleFieldBlur('signature')"
                  />
                  <p
                    v-else
                    style="flex: 1; cursor: pointer; margin: 0; white-space: nowrap;"
                    @click="startEditing('signature')"
                  >
                    {{ userStore.userInfo.signature || '这个人很懒，什么都没写~' }}
                  </p>
                  <el-icon
                    v-show="!isEditing.signature"
                    class="edit-icon"
                    style="margin-left: 12px; color: #666;"
                    @click="startEditing('signature')"
                  >
                    <Edit />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <el-tab-pane label="我的攻略" name="guides">
            <div class="guides-grid">
              <el-card
                v-for="guide in userGuides"
                :key="guide.id"
                class="guide-card"
                :body-style="{ padding: '0px' }"
              >
                <div class="guide-image">
                  <img :src="guide.cover" :alt="guide.title">
                  <div class="guide-overlay">
                    <el-button type="primary" size="small" @click="viewGuide(guide)">
                      查看详情
                    </el-button>
                  </div>
                </div>
                <div class="guide-content">
                  <h3>{{ guide.title }}</h3>
                  <p class="guide-desc">{{ guide.description }}</p>
                  <div class="guide-meta">
                    <span class="guide-date">
                      <el-icon><Calendar /></el-icon>
                      {{ formatDate(guide.createTime) }}
                    </span>
                    <span class="guide-views">
                      <el-icon><View /></el-icon>
                      {{ guide.views || 0 }}
                    </span>
                  </div>
                </div>
              </el-card>
            </div>
          </el-tab-pane>

          <el-tab-pane label="账号安全" name="security">
            <div class="security-settings">
              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>修改密码</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showChangePassword = true"
                    >
                      修改
                    </el-button>
                  </div>
                </template>
                <p>定期修改密码可以保护账号安全</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>手机绑定</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showBindPhone = true"
                    >
                      {{ userStore.userInfo.phone ? '修改' : '绑定' }}
                    </el-button>
                  </div>
                </template>
                <p>{{ userStore.userInfo.phone || '未绑定手机号' }}</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>邮箱绑定</span>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="showBindEmail = true"
                      class="action-btn"
                    >
                      {{ userStore.userInfo.email ? '修改' : '绑定' }}
                    </el-button>
                  </div>
                </template>
                <p>{{ userStore.userInfo.email || '未绑定邮箱' }}</p>
              </el-card>

              <el-card class="security-card">
                <template #header>
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>登录状态</span>
                    <el-button
                      type="danger"
                      link
                      size="small"
                      class="action-btn"
                      @click="handleLogout"
                    >
                      立即注销
                    </el-button>
                  </div>
                </template>
                <p>当前登录账号：{{ userStore.userInfo.account }}</p>
                <!-- <p>登录有效期至：{{ new Date(userStore.passwordExpire).toLocaleString() }}</p> -->
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showChangePassword"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            show-password
            @input="handleInput('currentPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            @input="handleInput('newPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model.trim="passwordForm.confirmPassword"
            type="password"
            show-password
            @input="handleInput('confirmPassword')"
            @compositionstart="isComposing = true"
            @compositionend="isComposing = false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer" style="display: flex; justify-content: flex-end; gap: 12px;">
          <el-button size="small" @click="showChangePassword = false">取消</el-button>
          <el-button type="primary" size="small" @click="handleChangePassword">确认修改</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { useUserStore } from './userStore'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Check, Calendar, View, Edit } from '@element-plus/icons-vue'
import { useRouter } from 'vitepress'
import { storeToRefs } from 'pinia'

const router = useRouter()
const userStore = useUserStore()
const { isLoggedIn } = storeToRefs(userStore)
const activeTab = ref('guides')
const showChangePassword = ref(false)
const showBindPhone = ref(false)
const showBindEmail = ref(false)

// 编辑状态管理
const showEdit = reactive({
  nickname: false,
  signature: false
})

const isEditing = reactive({
  nickname: false,
  signature: false
})

const editForm = reactive({
  nickname: '',
  signature: ''
})

// 初始化编辑表单
const initEditForm = () => {
  editForm.nickname = userStore.userInfo.nickname || ''
  editForm.signature = userStore.userInfo.signature || ''
}
initEditForm()

// 开始编辑
const startEditing = async (field) => {
  isEditing[field] = true
  await nextTick()
  if (field === 'nickname') {
    document.querySelector('.nickname-input .el-input__inner').focus()
  } else {
    document.querySelector('.signature-input .el-textarea__inner').focus()
  }
}

// 字段失焦处理
const handleFieldBlur = async (field) => {
  isEditing[field] = false
  if (editForm[field] !== userStore.userInfo[field]) {
    try {
      await userStore.updateProfile({ [field]: editForm[field] })
      ElMessage.success({
        message: '更新成功',
        duration: 1000
      })
      userStore.userInfo[field] = editForm[field]
    } catch (error) {
      ElMessage.error({
        message: '更新失败',
        duration: 1000
      })
      editForm[field] = userStore.userInfo[field]
    }
  }
}

// 用户信息
const userInfo = ref({
  avatar: '',
  isVerified: false,
  guideCount: 0,
  followers: 0,
  following: 0,
  phone: '',
  email: '',
})

// 密码表单
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const isComposing = ref(false)
const passwordFormRef = ref(null)

// 输入实时处理
const handleInput = (field) => {
  if (!isComposing.value) {
    // 移除trim()保留原始输入
    passwordFormRef.value.validateField(field)
    // 当修改新密码时自动验证确认密码
    if (field === 'newPassword') {
      passwordFormRef.value.validateField('confirmPassword')
    }
  }
}

// 密码验证规则
const passwordRules = reactive({
  currentPassword: [
    { required: true, message: '当前密码不能为空', trigger: ['blur', 'change'] }
  ],
  newPassword: [
    {
      required: true,
      pattern: /^(?=.*[a-zA-Z])(?=.*\d)[\x21-\x7e]{6,20}$/,
      message: '需6-20位, 必须包含字母和数字',
      trigger: ['blur', 'input']
    },
    {
      validator: (rule, value, callback) => {
        if (value === passwordForm.value.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      },
      trigger: ['input', 'blur']
    }
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (!value || !passwordForm.value.newPassword) {
          return callback()
        }
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: ['input', 'blur']
    }
  ]
})

// 用户攻略列表
const userGuides = ref([])

// 处理头像上传
const handleAvatarChange = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  const isImage = validTypes.includes(file.raw.type)
  const isLt10M = file.raw.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error({
      message: '只能上传图片文件!',
      duration: 1000
    })
    return false
  }
  if (!isLt10M) {
    ElMessage.error({
      message: '图片大小不能超过10MB!',
      duration: 1000
    })
    return false
  }

  uploadAvatar(file.raw)
  return true
}

// 上传头像
const uploadAvatar = async (file) => {
  try {
    const result = await userStore.updateAvatar(file)
    if (result.success) {
      ElMessage.success({
        message: '头像更新成功',
        duration: 1000
      })
      userStore.userInfo.avatar = result.avatarUrl
    } else {
      ElMessage.error({
        message: result.message || '头像更新失败',
        duration: 1000
      });
    }
  } catch (error) {
    ElMessage.error({
      message: '头像上传失败: ' + (error.message || '未知错误'),
      duration: 1000
    })
  }
}

// 处理修改密码
const handleChangePassword = async () => {
  try {
    // 修正为调用表单引用的validate方法
    await passwordFormRef.value.validate()

    const result = await userStore.changePassword({
      currentPassword: passwordForm.value.currentPassword, // 明文传递
      newPassword: passwordForm.value.newPassword          // 明文传递
    })

    if (result.success) {
      ElMessage.success({ message: '密码更新成功', duration: 1000 })
      // 清除表单数据应使用表单引用的resetFields方法
      passwordFormRef.value.resetFields()
      showChangePassword.value = false
    }
  } catch (error) {
    console.error('密码修改失败:', error)
    ElMessage.error({
      message: error.response?.data?.message || '密码修改失败',
      duration: 1500
    })
  }
}

// 查看攻略详情
const viewGuide = (guide) => {
  console.log('查看攻略:', guide)
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

// 跳转到登录页面
const goToLogin = () => {
  router.go('/user-center/login')
}

// 注销处理
const handleLogout = () => {
  ElMessageBox.confirm('确定要注销当前登录吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout();
    userStore.clearStoredPassword();
    router.go('/user-center/login');
    ElMessage.success({
      message: '已成功注销',
      duration: 2000
    })
  }).catch(() => {});
};
</script>

<style scoped>
.user-profile {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.profile-header {
  position: relative;
}

.profile-cover {
  height: 200px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  position: relative;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.4));
}

.profile-info {
  position: relative;
  padding: 0 2rem 2rem;
  margin-top: -60px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.avatar-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 0.5rem;
  border-radius: 0 0 60px 60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-wrapper:hover .avatar-actions {
  opacity: 1;
}

.upload-btn {
  width: 100%;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.user-details {
  margin-top: 1rem;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-name h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-signature {
  margin: 0.5rem 0;
  color: #666;
  font-size: 1rem;
  position: relative;
}

.editable-field {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

/* 统一按钮间距 */
.card-header .el-button {
  margin-left: auto;
  padding: 6px 12px;
}

/* 输入框防换行 */
.editable-field :deep(.el-input__inner) {
  white-space: nowrap;
  max-width: 400px;
}

.editable-field:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  color: #409eff;
  margin-left: 8px;
  cursor: pointer;
  transition: opacity 0.3s;
  opacity: 0;
}

.edit-icon:hover {
  color: #409eff !important;
  cursor: pointer;
}

/* 对话框按钮容器 */
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-input, .el-textarea {
  width: 80%;
}

.profile-content {
  background: #fff;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.guides-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.guide-card {
  transition: transform 0.3s;
}

.guide-card:hover {
  transform: translateY(-5px);
}

.guide-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.guide-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.guide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.guide-card:hover .guide-overlay {
  opacity: 1;
}

.guide-card:hover .guide-image img {
  transform: scale(1.1);
}

.guide-content {
  padding: 1rem;
}

.security-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (max-width: 768px) {
  .user-profile {
    padding: 1rem;
  }

  .profile-info {
    padding: 0 1rem 1rem;
  }

  .guides-grid {
    grid-template-columns: 1fr;
  }
}

.login-prompt {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.login-prompt :deep(.el-alert) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 1.5rem;
  text-align: center;
}

.login-prompt :deep(.el-alert__content) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
}

@media (max-width: 768px) {
  .login-prompt {
    padding: 0 1rem;
  }
}
</style>