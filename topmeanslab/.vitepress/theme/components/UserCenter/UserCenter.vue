<template>
  <div class="user-center">
    <!-- 未登录状态显示登录/注册界面 -->
    <!-- <div v-if="!isLoggedIn" class="auth-container"> -->
    <div v-if="!userStore.isLoggedIn" class="auth-container">
      <div class="auth-box">
        <div class="auth-tabs">
          <button
            :class="['tab-btn', { active: activeTab === 'login' }]"
            @click="activeTab = 'login'"
          >
            登录
          </button>
          <button
            :class="['tab-btn', { active: activeTab === 'register' }]"
            @click="activeTab = 'register'"
          >
            注册
          </button>
        </div>

        <!-- 登录表单 -->
        <div v-if="activeTab === 'login'" class="auth-form">
          <div class="form-group">
            <label>账号</label>
            <input
              v-model="loginForm.account"
              type="text"
              placeholder="请输入手机号或邮箱"
              required
            >
          </div>
          <div class="form-group">
            <label>密码</label>
            <input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              required
            >
          </div>
          <div class="form-group">
            <label>验证码</label>
            <input
              v-model="loginForm.valicode"
              type="text"
              placeholder="请输入验证码, 并确保大小写正确"
              required
            >
            <Valicode ref="valicode" @getCode="handleGetCode" />
          </div>
          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="loginForm.remember">
              记住密码
            </label>
            <a href="#" class="forgot-password" @click="handleForgotPassword">忘记密码？</a>
          </div>
          <button @click="handleLogin" class="submit-btn">登录</button>
        </div>

        <!-- 注册表单 -->
        <div v-if="activeTab === 'register'" class="auth-form">
          <div class="form-group">
            <label>账号</label>
            <input
              v-model="registerForm.account"
              type="text"
              placeholder="请输入你的账号名称"
              required
            >
          </div>
          <div class="form-group">
            <label>密码</label>
            <input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码, 由6位以上的英文和数组组成"
              required
            >
          </div>
          <div class="form-group">
            <label>确认密码</label>
            <input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码, 确保两次密码一致"
              required
            >
          </div>
          <div class="form-group">
            <label>验证码</label>
            <input
              v-model="loginForm.valicode"
              type="text"
              placeholder="请输入验证码, 并确保大小写正确"
              required
            >
            <Valicode ref="valicode" @getCode="handleGetCode" />
          </div>
          <button @click="handleRegister" class="submit-btn">注册</button>
        </div>
      </div>
    </div>

    <!-- 已登录状态显示用户资料 -->
    <div v-else class="profile-container">

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vitepress'
import 'vue-toastification/dist/index.css'
import { useUserStore } from './userStore'
import { ElMessage } from 'element-plus'
import Valicode from './Valicode.vue'; // 确保路径正确
import { reactive } from 'vue';
import log from 'loglevel';

const API_BASE = `${import.meta.env.VITE_BACKEND_SRV_URL}/api`;
log.setLevel('info'); // 设置日志级别

// 状态管理
const isLoggedIn = ref(false);
const activeTab = ref('login');
const user = ref(null);
const userStore = useUserStore()
const router = useRouter()

const handleGetCode = (code) => {
  generatedCode.value = code;
};

// 表单数据
const loginForm = ref({
  account: '',
  password: '',
  valicode: '',
  remember: false
});

const registerForm = ref({
  account: '',
  password: '',
  confirmPassword: '',
  valicode: '',
  nickname: '',
  avatar: '/images/default-avatar.jpg',
  signature: '这个人很懒，什么都没写~',
});

// 存储生成的验证码
const valicode = ref(null);
const generatedCode = ref('');

// 登录处理
const handleLogin = async () => {

  if (!loginForm.value.account || !loginForm.value.password) {
    ElMessage.error({
      message: '请输入账号和密码',
      duration: 1000
    });
    valicode.value?.refresh()
    return;
  }

  if (loginForm.value.valicode !== generatedCode.value) {
    ElMessage.error({
      message: '验证码错误，请重新输入',
      duration: 1000
    });
    valicode.value?.refresh()
    return;
  }

  let retryCount = 0;
  const maxRetries = 2;

    try {
      const response = await fetch(`${API_BASE}/user/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          account: loginForm.value.account,
          password: loginForm.value.password
        }),
        signal: AbortSignal.timeout(8000) // 8秒超时
      });

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const result = await response.json();

      if (result.success) {
        // 根据记住密码选项处理密码存储
        if (loginForm.value.remember) {
          userStore.setStoredPassword(loginForm.value.password)
        } else {
          userStore.clearStoredPassword()
        }
        // 保存登录状态到 store
        userStore.setToken(result.token)
        userStore.setUserInfo(result.user)
        userStore.setStoredPassword(loginForm.value.remember)

        // 自动填充账号逻辑
        if (loginForm.value.remember) {
          localStorage.setItem('rememberedAccount', loginForm.value.account)
        } else {
          localStorage.removeItem('rememberedAccount')
        }

        // 1秒后跳转
        ElMessage.success({
          message: '登录成功，正在跳转...',
          duration: 500,
          onClose: () => {
            router.go('/user-center/profile')
          }
        })
      } else {
        ElMessage.error({
          message: result.message || '登录失败',
          duration: 1000
        });
        valicode.value?.refresh();
        return
      }
    } catch (error) {
      retryCount++;
      log.error(`登录尝试 ${retryCount} 失败:`, error);

      if (retryCount > maxRetries) {
        ElMessage.error({
          message: error.message.includes('timeout')
            ? '请求超时，请检查网络'
            : '服务不稳定，请稍后重试',
          duration: 1000
        });
        valicode.value?.refresh();
      } else {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1秒后重试
        valicode.value?.refresh();
      }
    }

};


// 注册处理
const handleRegister = async () => {
  if (!registerForm.value.account || !registerForm.value.password) {
    ElMessage.error('请输入账号和密码');
    valicode.value?.refresh();
    return;
  }

    // 密码一致性校验
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致，请重新输入!');
    valicode.value?.refresh();
    return;
  }

  if (loginForm.value.valicode !== generatedCode.value) {
    ElMessage.error({
      message: '验证码错误，请重新输入',
      duration: 1000
    });
    valicode.value?.refresh()
    return;
  }

  try {
    const response = await fetch(`${API_BASE}/user/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(registerForm.value)
    });

    const result = await response.json();

    if (result.success) {
      ElMessage.success('注册成功，请登录');
      activeTab.value = 'login';
      loginForm.value.account = registerForm.value.account;
      registerForm.value = { account: '', password: '', nickname: '', avatar: '/images/default-avatar.jpg', signature: '这个人很懒，什么都没写~' };
    } else {
      ElMessage.error(result.message || '注册失败');
      valicode.value?.refresh();
    }
  } catch (error) {
    log.error('注册错误:', error);
    ElMessage.error('网络错误，请稍后重试');
    valicode.value?.refresh();
  }
};

onMounted(() => {
  if (userStore.checkLoginStatus()) {
    router.go('/user-center/profile');
  }

  // 自动填充密码
  if (userStore.storedPassword && localStorage.getItem('rememberedAccount')) {
    const remembered = localStorage.getItem('rememberedAccount');
    loginForm.value.account = remembered;
    loginForm.value.password = userStore.storedPassword;
    loginForm.value.remember = true;
  }
});
</script>


<style scoped>
.user-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 认证相关样式 */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.auth-box {
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 2px solid #eee;
}

.tab-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  background: none;
  font-size: 1.1rem;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-btn.active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  margin-bottom: -2px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  color: #666;
}

.form-group input {
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
}

.form-group input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.submit-btn {
  padding: 1rem;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s;
}

.submit-btn:hover {
  background: #40a9ff;
}

/* 个人资料相关样式 */
.profile-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-header {
  display: flex;
  padding: 2rem;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid white;
  object-fit: cover;
}

.change-avatar-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid white;
  color: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.change-avatar-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.user-info {
  margin-left: 2rem;
}

.user-info h2 {
  margin: 0;
  font-size: 1.8rem;
}

.signature {
  margin: 0.5rem 0 0;
  opacity: 0.9;
}

.profile-content {
  padding: 2rem;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.edit-btn {
  padding: 0.5rem 1rem;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
}

.edit-btn:hover {
  background: #40a9ff;
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 600px;
}

.form-actions {
  display: flex;
  gap: 1rem;
}

.save-btn, .cancel-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.save-btn {
  background: #1890ff;
  color: white;
  border: none;
}

.save-btn:hover {
  background: #40a9ff;
}

.cancel-btn {
  background: white;
  color: #666;
  border: 1px solid #ddd;
}

.cancel-btn:hover {
  background: #f5f5f5;
}

.travel-guides {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.guide-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.guide-card:hover {
  transform: translateY(-5px);
}

.guide-cover {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.guide-info {
  padding: 1rem;
}

.guide-info h4 {
  margin: 0 0 0.5rem;
}

.guide-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.guide-date {
  display: block;
  margin-top: 0.5rem;
  color: #999;
  font-size: 0.8rem;
}

.login-tabs {
  margin-bottom: 2rem;
}

.login-form,
.register-form {
  margin-top: 1rem;
}

.submit-btn {
  width: 100%;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

@media (max-width: 768px) {
  .user-center {
    margin: 1rem;
    padding: 1rem;
  }
}

.user-actions {
  margin-left: auto;
  display: flex;
  gap: 1rem;
}

.logout-btn, .delete-account-btn {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid white;
  color: white;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.delete-account-btn {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.6);
  color: white;
}

.delete-account-btn:hover {
  background: rgba(255, 0, 0, 0.3);
}
</style>