import { marked } from 'marked';
import { useUserStore } from './UserCenter/userStore';
import log from 'loglevel';

// 导入服务模块
import { mapService } from './services/TopmeansMapService.js';
import { scrollManager } from './services/TopmeansScrollManager.js';
import { formManager } from './services/TopmeansFormManager.js';
import { apiService } from './services/TopmeansApiService.js';
import { styleManager } from './services/TopmeansStyleManager.js';

// 导入环境变量
const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;
const TRAVEL_PLAN_PROMPT = import.meta.env.VITE_TRAVEL_PLAN_PROMPT;

log.setLevel('info');

marked.setOptions({
    breaks: true,       // 转换 \n 为 <br>
    gfm: true,          // 支持 GitHub Flavored Markdown
    headerIds: false,    // 禁用自动生成 header IDs
    mangle: false,
    sanitize: false, // 允许 HTML 标签
});

export default {
    data() {
        return {
            contents: [],
            showBtn: true,
            loading: false,
            errorMessage: '',
            last_end: '',
            rent_requirements: '',
            rent_customized: false,
            // 表单数据
            ...formManager.getDefaultFormData(),
        }
    },
    async mounted() {
        // 确保只在客户端执行
        if (typeof window === 'undefined') return;

        try {
            // 初始化地图服务
            await mapService.initialize();

            // 设置地址自动完成
            mapService.setupAutoComplete(
                "start-tipinput",
                "end-tipinput",
                (address) => { this.s_address = address; },
                (address) => { this.e_address = address; }
            );

            // 确保高德地图提示框样式正确应用
            styleManager.ensureAmapSuggestStyles();
        } catch (err) {
            this.errorMessage = err.message;
            log.error('地图初始化失败:', err);
        }

        // 初始化滚动监听器
        scrollManager.initScrollListener();

        // 设置内容完成检查函数
        scrollManager.setContentCompletedChecker(() => this.showBtn);

        // 恢复表单数据
        this.loadFormData();
    },
    beforeUnmount() {
        // 清理所有服务
        scrollManager.cleanup();
        styleManager.cleanup();
        mapService.cleanup();
        apiService.cleanup();
    },
    watch: {
        contents: {
            handler(newVal) {
                if (newVal.length > 0 && scrollManager.getScrollState().autoScrollEnabled) {
                    this.$nextTick(() => {
                        scrollManager.smartScrollToContent();
                    });
                }
            },
            deep: true
        },
        // 监听表单数据变化，自动保存到本地存储
        s_address: {
            handler() {
                this.saveFormData();
            }
        },
        e_address: {
            handler() {
                this.saveFormData();
            }
        },
        startDate: {
            handler() {
                this.saveFormData();
            }
        },
        dates: {
            handler() {
                this.saveFormData();
            }
        },
        plan_mode: {
            handler() {
                this.saveFormData();
            }
        },
        travel_mode: {
            handler() {
                this.saveFormData();
            }
        }
    },
    methods: {
        parseMarkdown(text) {
            return marked.parse(text || '');
        },
        // 保留原方法名以兼容其他调用
        scrollPageToBottom() {
            scrollManager.smartScrollToContent();
        },

        // 重置滚动状态（在开始新一轮操作时调用）
        resetScrollState() {
            scrollManager.resetScrollState();
        },

        // 保存表单数据到本地存储
        saveFormData() {
            const formData = {
                s_address: this.s_address,
                e_address: this.e_address,
                startDate: this.startDate,
                dates: this.dates,
                plan_mode: this.plan_mode,
                travel_mode: this.travel_mode
            };
            formManager.saveFormData(formData);
        },

        // 从本地存储加载表单数据
        loadFormData() {
            const savedData = formManager.loadFormData();
            if (savedData) {
                const mergedData = formManager.mergeFormData({
                    s_address: this.s_address,
                    e_address: this.e_address,
                    startDate: this.startDate,
                    dates: this.dates,
                    plan_mode: this.plan_mode,
                    travel_mode: this.travel_mode
                }, savedData);

                // 更新组件数据
                Object.assign(this, mergedData);
            }
        },

        // 清除本地存储的表单数据
        clearFormData() {
            formManager.clearFormData();
        },

        // 重置表单到默认值
        resetFormData() {
            const defaultData = formManager.resetFormData();
            Object.assign(this, defaultData);
        },

        // 更新租车需求输入框的值
        updateRentRequirements(event) {
            this.rent_requirements = event.target.value;
        },

        // 处理租车需求输入框的回车键事件
        handleRentCustomize(index) {
            if (this.rent_requirements && this.rent_requirements.trim()) {
                this.handleActionClick('rent', index);
            }
        },
        async drivingPlanning(index, start, end) {
            try {
                // 确保地图容器已渲染
                await this.$nextTick();
                await this.$nextTick(); // 双重确保

                await mapService.drivingPlanning(index, start, end);
            } catch (err) {
                log.error('路线生成错误', err);
                this.errorMessage = err.message || '路线生成失败';
            } finally {
                this.loading = false;
            }
        },
        // 存库
        async savePlanToDB(index, account, formattedDateTime) {
            // 保存截图
            await this.saveMapAsImage(index, account, formattedDateTime);

            let md_content = `# Smart Travel Plan\n\n## ${this.s_address} 到 ${this.e_address}\n\n`;
            if (this.contents[index]['rent']) {
                md_content += `${this.contents[index]['rent']}\n`;
            }

            md_content += `${this.contents[index]['plan']}\n`;
            md_content += `![路线规划](./map-${formattedDateTime}-${index}.png)\n`;

            for (let i = 0; i < this.contents[index]['view'].length; i++) {
                md_content += `${this.contents[index]['view'][i]['info']}\n`;
                if (this.contents[index]['view'][i]['url']) {
                    md_content += `![${this.contents[index]['view'][i]['name']}](${this.contents[index]['view'][i]['url']})\n`;
                }
            }

            for (let i = 0; i < this.contents[index]['hotel'].length; i++) {
                md_content += `${this.contents[index]['hotel'][i]['info']}\n`;
                if (this.contents[index]['hotel'][i]['url']) {
                    md_content += `![携程直达：${this.contents[index]['hotel'][i]['name']}](${this.contents[index]['hotel'][i]['url']})\n`;
                }
            }

            for (let i = 0; i < this.contents[index]['food'].length; i++) {
                md_content += `${this.contents[index]['food'][i]['info']}\n`;
                md_content += `![${this.contents[index]['food'][i]['name']}](${this.contents[index]['food'][i]['url']})\n`;
            }

            md_content += `${this.contents[index]['cost']}\n`;

            let response = await fetch(`${BACKEND_SRV_URL}/api/save_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: md_content,
                    user: account,
                    filename: `plan-${formattedDateTime}-${index}.md`, // 文件名
                })
            });
            if (!response.ok) {
                log.error('保存计划失败，请检查网络连接', response);
                throw new Error('保存计划失败，请检查网络连接');
            }

            // 存库
            response = await fetch(`${BACKEND_SRV_URL}/api/user/add_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account: account,
                    create_time: formattedDateTime,
                    days: this.dates,
                })
            });
            if (!response.ok) {
                log.error('计划存库失败，请检查网络连接', response);
                throw new Error('计划存库失败，请检查网络连接');
            }
        },
        // 保存地图为图片
        async saveMapAsImage(index, account, formattedDateTime) {
            try {
                await mapService.saveMapAsImage(index, account, formattedDateTime);
            } catch (err) {
                log.error('保存地图为图片失败:', err);
                throw err;
            }
        },
        async getHotelUrl(hotelName, account, formattedDateTime, day) {
            try {
                return await apiService.getHotelUrl(hotelName, account, formattedDateTime, day, this.last_end);
            } catch (err) {
                log.error('酒店信息获取失败:', err);
                throw err;
            }
        },
        async getFoodImgUrl(foodName, foodInfo) {
            try {
                return await apiService.getFoodImgUrl(foodName, foodInfo);
            } catch (err) {
                log.error('美食图片获取失败:', err);
                throw err;
            }
        },
        async getViewUrl(viewName) {
            try {
                return await apiService.getViewUrl(viewName);
            } catch (err) {
                log.error('景点信息获取失败:', err);
                throw err;
            }
        },
        async askDeepSeek(index, msg, type) {
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ msg })
                });
                if (!response.ok) {
                    log.error(`DS API 请求失败:${response.statusText}`);
                    throw new Error('DS API 请求失败!');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let result = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 处理流式数据
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            if (!line.startsWith('data: ')) continue;

                            const jsonStr = line.slice(6);
                            if (jsonStr === '[DONE]') break;

                            const data = JSON.parse(jsonStr);
                            const msg = data.choices[0].delta.content;

                            if (msg) {
                                result += msg;
                            }

                            if (type === 'rent') {
                                this.contents[index]['rent'] = `**租车建议**\n${result}`;
                            }
                        } catch (err) {
                            log.error('解析数据失败:', err);
                        }
                    }
                }
            }   catch (err) {
                log.error('DS API 请求失败:', err);
                throw err;
            }
        },
        async sendMessage(index, msg, formattedDateTime) {
            this.contents.push({
                rentCompleted: false,
                planCompleted: false,
                viewCompleted: false,
                hotelCompleted: false,
                foodCompleted: false,
                costCompleted: false
            });
            const us = useUserStore();
            const { user } = await us.getUserInfo();
            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/ds`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ msg })
                });
                if (!response.ok) {
                    log.error('DS API 请求失败!');
                    throw new Error('DS API 请求失败!');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let result = '';

                let rent = '';
                let start = '';
                let end = '';
                let distance = '';
                let serviceNum = '';
                let avoid = '';
                let vi = '';
                let ho = '';
                let fo = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    // 处理流式数据
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n').filter(line => line.trim());

                    for (const line of lines) {
                        try {
                            if (!line.startsWith('data: ')) continue;

                            const jsonStr = line.slice(6);
                            if (jsonStr === '[DONE]') break;

                            const data = JSON.parse(jsonStr);
                            const msg = data.choices[0].delta.content;

                            if (msg) {
                                result += msg;
                            }
                        } catch (err) {
                            log.error('解析数据失败:', err);
                        }

                        if (this.travel_mode === '租车') {
                            if (result.includes('#ST#') && rent === '') rent = result.split('#RT#')[1].split('#ST#')[0].trim();
                        }
                        if (result.includes('#EN#') && start === '') start = result.split('#ST#')[1].split('#EN#')[0].trim();
                        if (result.includes('#DI#') && end === '') {
                            end = result.split('#EN#')[1].split('#DI#')[0].trim();
                            this.contents[index]['amap'] = '';
                            // 等待地图容器加载
                            await this.$nextTick();

                            // 开始路线规划
                            try {
                                await this.drivingPlanning(index, start, end);
                            } catch (err) {
                                log.error('导航规划失败:', err);
                            }
                        }
                        if (result.includes('#SN#') && distance === '') distance = result.split('#DI#')[1].split('#SN#')[0];
                        if (result.includes('#AD#') && serviceNum === '') serviceNum = result.split('#SN#')[1].split('#AD#')[0];
                        if (result.includes('#VI#') && avoid === '') avoid = result.split('#AD#')[1].split('#VI#')[0];
                        if (result.includes('#HO#') && vi === '') vi = result.split('#VI#')[1].split('#HO#')[0];
                        if (result.includes('#FO#') && ho === '') ho = result.split('#HO#')[1].split('#FO#')[0];
                        if (result.includes('#CO#') && fo === '') fo = result.split('#FO#')[1].split('#CO#')[0];

                        // 将原始模板回答内容转为易读格式
                        // 租车建议只在第1天出现
                        if (index === 0 && this.travel_mode === '租车') {
                            if (result.includes('#RT#') && start === '' && !result.split('#RT#')[1].includes('#')) {
                                this.contents[index]['rent'] = `**租车建议**\n${result.split('#RT#')[1]}`;
                            }
                        }

                        if (result.includes('#ST#') && start === '' && !result.split('#ST#')[1].includes('#')) {
                            if (index === 0 && this.travel_mode === '租车' && rent !== '$$$') {
                                this.contents[index]['rent'] = `**租车建议**\n${rent}`;
                                rent = '$$$'; // 标记结束
                                // 标记租车建议完成
                                this.contents[index].rentCompleted = true;
                            }
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${result.split('#ST#')[1]}`;
                        }

                        if (result.includes('#EN#') && end === '' && !result.split('#EN#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${result.split('#EN#')[1]}`;
                        }

                        if (result.includes('#DI#') && distance === '' && !result.split('#DI#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${result.split('#DI#')}`;
                        }

                        if (result.includes('#SN#') && serviceNum === '' && !result.split('#SN#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${result.split('#SN#')[1]}`;
                        }

                        if (result.includes('#AD#') && avoid === '' && !result.split('#AD#')[1].includes('#')) {
                            this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${serviceNum}\n**避堵建议：** ${result.split('#AD#')[1]}`;
                        }

                        if (result.includes('#VI#') && vi === '' && !result.split('#VI#')[1].includes('#')) {
                            if (avoid !== '$$$') {
                                this.contents[index]['plan'] = `**第${index + 1}天的规划**\n\n**起点：**\t${start}\n**终点：**\t${end}\n**里程：** ${distance}\n**服务区：** ${serviceNum}\n**避堵建议：** ${avoid}`
                                avoid = '$$$'; // 标记结束
                                // 标记规划完成
                                this.contents[index].planCompleted = true;
                                this.contents[index]['view'] = [];
                            }

                            const vi_parts = result.split('#VI#')[1].split('*VINAME*');
                            for (let i = 0; i < vi_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['view'].length) this.contents[index]['view'].push({});
                                this.contents[index]['view'][i - 1]['info'] = '**景点信息:** ';
                                if (vi_parts[i].includes('**景点信息:**')) {
                                    this.contents[index]['view'][i - 1]['info'] += vi_parts[i].split('**景点信息:**')[1].trim()
                                    this.contents[index]['view'][i - 1]['name'] = vi_parts[i].split('**景点信息:**')[0].trim().replace(/\*/g, '');
                                    if (!('url' in this.contents[index]['view'][i - 1])) {
                                        // 等待 5 秒，否则爬取图片容易被反制
                                        await new Promise(resolve => setTimeout(resolve, 5000));
                                        this.contents[index]['view'][i - 1]['url'] = await this.getViewUrl(this.contents[index]['view'][i - 1]['name']);
                                    }
                                }
                            }
                        }

                        if (result.includes('#HO#') && ho === '' && !result.split('#HO#')[1].includes('#')) {
                            if (vi !== '$$$') {
                                // this.contents[index]['view'] = `\n\n\n\n**沿途景点：**\n${vi}`;
                                vi = '$$$'; // 标记结束
                                // 标记景点推荐完成
                                this.contents[index].viewCompleted = true;
                                this.contents[index]['hotel'] = [];
                            }

                            const ho_parts = result.split('#HO#')[1].split('*HONAME*');
                            for (let i = 0; i < ho_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['hotel'].length) this.contents[index]['hotel'].push({});
                                this.contents[index]['hotel'][i - 1]['info'] = '**酒店信息:** ';
                                if (ho_parts[i].includes('**酒店信息:**')) {
                                    this.contents[index]['hotel'][i - 1]['info'] += ho_parts[i].split('**酒店信息:**')[1].trim()
                                    this.contents[index]['hotel'][i - 1]['name'] = ho_parts[i].split('**酒店信息:**')[0].trim().replace(/\*/g, '');
                                    if (!('url' in this.contents[index]['hotel'][i - 1])) {
                                        this.contents[index]['hotel'][i - 1]['url'] = await this.getHotelUrl(this.contents[index]['hotel'][i - 1]['name'], user.account, formattedDateTime, index + 1);
                                    }
                                }
                            }
                        }

                        if (result.includes('#FO#') && fo === '' && !result.split('#FO#')[1].includes('#')) {
                            if (ho !== '$$$') {
                                ho = '$$$'; // 标记结束
                                // 标记酒店推荐完成
                                this.contents[index].hotelCompleted = true;
                                this.contents[index]['food'] = [];
                            }

                            const fo_parts = result.split('#FO#')[1].split('*FONAME*');
                            for (let i = 0; i < fo_parts.length; i++) {
                                if (i === 0) continue; // 跳过第一个部分
                                if (i > this.contents[index]['food'].length) this.contents[index]['food'].push({});
                                this.contents[index]['food'][i - 1]['info'] = '**美食信息:** ';
                                if (fo_parts[i].includes('**美食信息:**')) {
                                    this.contents[index]['food'][i - 1]['info'] += fo_parts[i].split('**美食信息:**')[1].trim()
                                    this.contents[index]['food'][i - 1]['name'] = fo_parts[i].split('**美食信息:**')[0].trim().replace(/\*/g, '');
                                }
                            }
                        }

                        if (result.includes('#CO#')) {
                            if (fo !== '$$$') {
                                for (let i = 0; i < this.contents[index]['food'].length; i++) {
                                    this.contents[index]['food'][i]['url'] = await this.getFoodImgUrl(this.contents[index]['food'][i]['name'], this.contents[index]['food'][i]['info']);
                                }
                                fo = '$$$'; // 标记结束
                                // 标记美食推荐完成
                                this.contents[index].foodCompleted = true;
                            }
                            this.contents[index]['cost'] = `**预估当日费用：**\n${result.split('#CO#')[1]}`;
                            // 标记费用计算完成
                            this.contents[index].costCompleted = true;
                        }
                    }
                }

                // 存库
                await this.savePlanToDB(index, user.account, formattedDateTime);
                return end;
            } catch (error) {
                log.error(`API请求失败: ${error}`);
            }
            return null;
        },
        // 处理操作按钮点击事件
        async handleActionClick(type, index) {
            let processed = false;
            if (type === 'rent') {
                if (this.rent_requirements) {
                    // 结合用户需求重新定制租车prompt并调用deepseek api进行提问
                    let prompt = `你现在是一个自驾旅游专家，我计划从${this.s_address}到${this.e_address}自驾旅游，游玩天数为${this.dates}天，我希望自驾的方式出行，租车，你给出的租车建议是：${this.contents[index]['rent']}，但是我有一些定制化需求：${this.rent_requirements}，请保持上述回答格式和风格重新给出租车建议`;
                    this.contents[index]['rent'] = '';
                    await this.askDeepSeek(index, prompt, 'rent');
                    this.contents[index].rentCompleted = true;
                    this.rent_customized = true;
                    processed = true;
                }
            }

            if (processed) {
                // 重新进行存库
                const us = useUserStore();
                const { user } = await us.getUserInfo();
                const formattedDateTime = this.getFormattedDate();
                await this.savePlanToDB(index, user.account, formattedDateTime);
            }
        },
        validateFormData() {
            const formData = {
                s_address: this.s_address,
                e_address: this.e_address,
                startDate: this.startDate,
                dates: this.dates,
                plan_mode: this.plan_mode,
                travel_mode: this.travel_mode
            };

            const validation = formManager.validateFormData(formData);

            if (!validation.isValid) {
                alert(validation.message);
            }

            return validation.isValid;
        },
        getFormattedDate() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}${month}${day}`;
            const formattedTime = `${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}${String(date.getSeconds()).padStart(2, '0')}`;
            const formattedDateTime = `${formattedDate}${formattedTime}`;
            return formattedDateTime;
        },
        async planning() {
            // 校验用户是否登录
            const us = useUserStore();
            if (!us.checkLoginStatus()) {
                alert('请先登录');
                return;
            }

            // 数据校验
            if (!this.validateFormData()) {
                alert('请输入完整内容,包括起点，终点，开始日期，游玩天数');
                return;
            };

            // 隐藏按钮
            this.showBtn = false;

            // 重置滚动状态（在清空内容之前重置，确保自动滚动能够恢复）
            this.resetScrollState();

            // 状态初始化
            this.contents = [];

            // 获取时间
            const formattedDateTime = this.getFormattedDate();

            let rent_prompt = '';
            let rent_answer_format = '';
            if (this.travel_mode === '租车') {
                rent_prompt = '请针对油车和电车分别给出三个租车车辆类型推荐，具体到品牌和车型，然后给出价位和靠谱的租车公司';
                rent_answer_format = '#RT# "油车和电车三个租车车辆类型推荐，具体到品牌和车型，然后给出价位和靠谱的租车公司"';
            }

            // 按照游玩天数进行多轮次询问
            for (let i = 1; i <= this.dates; i++) {
                // 从环境变量中获取 prompt 模板
                let promptTemplate = TRAVEL_PLAN_PROMPT || '';

                // 替换模板中的变量
                promptTemplate = promptTemplate
                    .replace(/startDate/g, this.startDate)
                    .replace(/s_address/g, this.s_address)
                    .replace(/e_address/g, this.e_address)
                    .replace(/plan_mode/g, this.plan_mode)
                    .replace(/travel_mode/g, this.travel_mode)
                    .replace(/dates/g, this.dates)
                    .replace(/rent_prompt/g, rent_prompt)
                    .replace(/rent_answer_format/g, rent_answer_format)
                    .replace(/last_end/g, this.last_end);

                promptTemplate = promptTemplate.replace(/i/g, `${i}`);

                if (i > 1) {
                    promptTemplate += `，请注意，第${i - 1}天的规划已给出，终点是${this.last_end}，所以这次的起点应该是${this.last_end}`;
                }

                this.last_end = await this.sendMessage(i - 1, promptTemplate, formattedDateTime);
            }

            this.showBtn = true;
        }
    }
}