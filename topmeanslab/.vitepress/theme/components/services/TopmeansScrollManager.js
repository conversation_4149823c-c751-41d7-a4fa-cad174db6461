/**
 * 滚动管理器 - 负责页面滚动相关的所有操作
 */
export class TopmeansScrollManager {
    constructor() {
        // 滚动控制相关状态
        this.autoScrollEnabled = true;
        this.userScrollTimeout = null;
        this.lastScrollTop = 0;
        this.isUserScrolling = false;
        this.scrollObserver = null;
        this.handleUserScroll = null;
    }

    /**
     * 初始化滚动监听器
     */
    initScrollListener() {
        if (typeof window === 'undefined') return;

        // 监听用户滚动
        this.handleUserScroll = this.throttle(() => {
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollDiff = Math.abs(currentScrollTop - this.lastScrollTop);

            // 检测用户是否主动滚动（滚动幅度较大且不是自动滚动触发的）
            if (scrollDiff > 50 && this.autoScrollEnabled) {
                // 检查是否是向上滚动（用户查看之前的内容）
                const isScrollingUp = currentScrollTop < this.lastScrollTop;

                // 如果是向上滚动或大幅度滚动，认为是用户主动操作
                if (isScrollingUp || scrollDiff > 200) {
                    this.isUserScrolling = true;
                    this.autoScrollEnabled = false;

                    // 清除之前的定时器
                    if (this.userScrollTimeout) {
                        clearTimeout(this.userScrollTimeout);
                    }

                    // 5秒后恢复自动滚动
                    this.userScrollTimeout = setTimeout(() => {
                        this.isUserScrolling = false;
                        // 检查是否所有内容都已完成，如果是则不恢复自动滚动
                        if (!this.checkAllContentCompleted()) {
                            this.autoScrollEnabled = true;
                            // 恢复时滚动到最新内容
                            this.smartScrollToContent();
                        }
                    }, 5000);
                }
            }

            this.lastScrollTop = currentScrollTop;
        }, 150);

        window.addEventListener('scroll', this.handleUserScroll, { passive: true });
    }

    /**
     * 清理滚动监听器
     */
    cleanupScrollListener() {
        if (typeof window === 'undefined') return;

        if (this.handleUserScroll) {
            window.removeEventListener('scroll', this.handleUserScroll);
        }

        if (this.userScrollTimeout) {
            clearTimeout(this.userScrollTimeout);
        }
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    /**
     * 检查是否所有内容都已完成
     * 这个方法需要从外部传入检查函数
     */
    setContentCompletedChecker(checkerFunction) {
        this.checkAllContentCompleted = checkerFunction;
    }

    /**
     * 默认的内容完成检查函数
     */
    checkAllContentCompleted() {
        // 默认返回false，实际使用时应该从外部设置检查函数
        return false;
    }

    /**
     * 智能滚动到内容
     */
    smartScrollToContent() {
        if (!this.autoScrollEnabled || typeof window === 'undefined') return;

        // 检查是否所有内容都已完成，如果是则停止自动滚动
        if (this.checkAllContentCompleted()) {
            this.autoScrollEnabled = false;
            return;
        }

        try {
            // 找到最后一个有内容的 answer-area-container
            const containers = document.querySelectorAll('.answer-area-container');
            if (containers.length === 0) return;

            let targetContainer = null;
            let hasActiveLoading = false;

            // 从后往前找到第一个正在显示内容的容器（有加载指示器的）
            for (let i = containers.length - 1; i >= 0; i--) {
                const container = containers[i];
                const loadingIndicator = container.querySelector('.loading-indicator');

                // 如果有加载指示器且可见，说明正在处理，这是我们要滚动到的目标
                if (loadingIndicator &&
                    loadingIndicator.offsetParent !== null && // 检查元素是否可见
                    window.getComputedStyle(loadingIndicator).display !== 'none') {
                    targetContainer = container;
                    hasActiveLoading = true;
                    break;
                }
            }

            // 如果没有找到正在处理的容器，检查是否真的全部完成了
            if (!hasActiveLoading) {
                // 再次检查是否所有内容都已完成
                if (this.checkAllContentCompleted()) {
                    this.autoScrollEnabled = false;
                    return;
                }
                // 如果还有未完成的内容，滚动到最后一个容器
                if (containers.length > 0) {
                    targetContainer = containers[containers.length - 1];
                }
            }

            if (targetContainer) {
                // 滚动到容器底部附近，确保用户能看到正在生成的内容
                const containerRect = targetContainer.getBoundingClientRect();
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                // 计算目标滚动位置：容器底部 - 视窗高度的70%，确保容器在视窗中央偏下
                const viewportHeight = window.innerHeight;
                const targetScrollTop = scrollTop + containerRect.bottom - viewportHeight * 0.7;

                // 平滑滚动到目标位置
                window.scrollTo({
                    top: Math.max(0, targetScrollTop),
                    behavior: 'smooth'
                });
            } else {
                // 备用方案：滚动到页面底部
                window.scrollTo({
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                });
            }
        } catch (error) {
            // 备用方案：滚动到页面底部
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }
    }

    /**
     * 保留原方法名以兼容其他调用
     */
    scrollPageToBottom() {
        this.smartScrollToContent();
    }

    /**
     * 重置滚动状态（在开始新一轮操作时调用）
     */
    resetScrollState() {
        this.autoScrollEnabled = true;
        this.isUserScrolling = false;

        // 安全地获取当前滚动位置
        if (typeof window !== 'undefined') {
            this.lastScrollTop = window.pageYOffset || document.documentElement.scrollTop || 0;
        } else {
            this.lastScrollTop = 0;
        }

        // 清除用户滚动定时器
        if (this.userScrollTimeout) {
            clearTimeout(this.userScrollTimeout);
            this.userScrollTimeout = null;
        }
    }

    /**
     * 获取当前滚动状态
     */
    getScrollState() {
        return {
            autoScrollEnabled: this.autoScrollEnabled,
            isUserScrolling: this.isUserScrolling,
            lastScrollTop: this.lastScrollTop
        };
    }

    /**
     * 设置自动滚动状态
     */
    setAutoScrollEnabled(enabled) {
        this.autoScrollEnabled = enabled;
    }

    /**
     * 检查是否正在用户滚动
     */
    isUserScrollingActive() {
        return this.isUserScrolling;
    }

    /**
     * 强制滚动到页面底部
     */
    forceScrollToBottom() {
        if (typeof window === 'undefined') return;
        
        window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth'
        });
    }

    /**
     * 滚动到指定元素
     */
    scrollToElement(element, behavior = 'smooth') {
        if (!element || typeof window === 'undefined') return;

        element.scrollIntoView({
            behavior: behavior,
            block: 'center'
        });
    }

    /**
     * 清理所有资源
     */
    cleanup() {
        this.cleanupScrollListener();
        
        // 重置所有状态
        this.autoScrollEnabled = true;
        this.isUserScrolling = false;
        this.lastScrollTop = 0;
        this.userScrollTimeout = null;
        this.scrollObserver = null;
        this.handleUserScroll = null;
    }
}

// 创建单例实例
export const scrollManager = new TopmeansScrollManager();
