import html2canvas from 'html2canvas';
import log from 'loglevel';

const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;

/**
 * 地图服务类 - 负责高德地图相关的所有操作
 */
export class TopmeansMapService {
    constructor() {
        this.AMap = null;
        this.mapInstances = [];
    }

    /**
     * 初始化地图服务
     */
    async initialize() {
        if (typeof window === 'undefined') return;

        let sc = '';
        let sk = '';

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap_keys`, {
                method: 'GET',
                credentials: 'include'
            });
            if (!response.ok) {
                log.error('获取API密钥失败，请检查网络连接', response);
                throw new Error('获取API密钥失败，请检查网络连接');
            }

            const { AMAP_CODE, AMAP_KEY } = await response.json();
            sc = AMAP_CODE;
            sk = AMAP_KEY;
        } catch (err) {
            log.error('获取API密钥异常，请检查网络连接', err);
            throw err;
        }

        window._AMapSecurityConfig = { securityJsCode: sc };

        try {
            // 异步加载地图脚本
            this.AMap = await this.loadAMapScript(sk);
            return this.AMap;
        } catch (err) {
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 设置地址自动完成功能
     */
    setupAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        this.AMap.plugin(['AMap.AutoComplete'], () => {
            const startAuto = new this.AMap.AutoComplete({
                input: startInputId
            });

            startAuto.on("select", (e) => {
                onStartSelect(e.poi.name);
            });

            const endAuto = new this.AMap.AutoComplete({
                input: endInputId
            });

            endAuto.on("select", (e) => {
                onEndSelect(e.poi.name);
            });
        });
    }

    /**
     * 封装地图脚本加载
     */
    loadAMapScript(key) {
        return new Promise(async (resolve, reject) => {
            if (window.AMap) return resolve(window.AMap);

            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/amap`, {
                    method: 'GET',
                    credentials: 'include'
                });
                if (!response.ok) {
                    throw new Error('地图脚本加载失败，请检查网络连接');
                }

                const { scriptUrl } = await response.json();

                const script = document.createElement('script');
                script.src = scriptUrl;
                script.onload = () => resolve(window.AMap);
                script.onerror = (err) => reject(
                    new Error('地图脚本加载失败，请检查网络连接', { cause: err })
                );
                document.head.appendChild(script);
            } catch (err) {
                reject(new Error('地图脚本加载失败，请检查网络连接', { cause: err }));
            }
        });
    }

    /**
     * 封装地理编码操作
     */
    async getGeocodePosition(geocoder, address) {
        return new Promise((resolve, reject) => {
            geocoder.getLocation(address, (status, result) => {
                if (status === 'complete' && result.geocodes.length) {
                    resolve(result.geocodes[0].location);
                } else {
                    reject(new Error(`无法解析地址: ${address}`));
                }
            });
        });
    }

    /**
     * 初始化地图实例
     */
    async initMap(index, start, end) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        try {
            const map = new this.AMap.Map(`map-container-${index}`, {
                renderer: "canvas",
                resizeEnable: true,
                viewMode: "2D",
                crossOrigin: 'anonymous',
                WebGLParams: {
                    preserveDrawingBuffer: true
                }
            });

            this.mapInstances.push(map);

            // 构造路线导航类
            const driving = new this.AMap.Driving({
                map: map,
                panel: "",  // 指定空字符串禁用默认面板
                renderer: "canvas", // 使用Canvas绘制路线
            });

            // 根据起终点名称规划驾车导航路线
            driving.search([
                { keyword: start },
                { keyword: end }
            ], function (status, result) {
                if (status === 'complete') {
                    log.info('路线规划成功');
                } else {
                    log.error('获取驾车数据失败：' + result);
                }
            });

            return map;
        } catch (err) {
            log.error('地图初始化失败:', err);
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 驾车路线规划
     */
    async drivingPlanning(index, start, end) {
        try {
            const container = document.getElementById(`map-container-${index}`);
            if (!container) {
                log.error('地图容器未找到');
                throw new Error('地图容器未找到');
            }

            await this.initMap(index, start, end);
        } catch (err) {
            log.error('路线生成错误', err);
            throw new Error(err.message || '路线生成失败');
        }
    }

    /**
     * 保存地图为图片
     */
    async saveMapAsImage(index, account, formattedDateTime) {
        try {
            const mapContainer = document.getElementById(`map-container-${index}`);
            if (!mapContainer) {
                throw new Error('地图容器未找到');
            }

            // 使用 html2canvas 截取地图容器
            const canvas = await html2canvas(mapContainer, {
                useCORS: true,        // 启用跨域
                allowTaint: true,     // 允许污染模式
                logging: true,        // 开启日志查看问题
                scale: 2,             // 提高分辨率
                ignoreElements: (element) => {
                    // 忽略版权信息外的其他遮挡元素
                    return element.id === 'some-obstructive-element';
                },
            });

            // 将 Canvas 转换为 Base64 数据
            const imageData = canvas.toDataURL('image/png');

            // 发送图片数据到后端
            const response = await fetch(`${BACKEND_SRV_URL}/api/save_amap_img`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: imageData, // Base64 图片数据
                    user: account,
                    filename: `map-${formattedDateTime}-${index}.png` // 文件名
                })
            });

            if (!response.ok) {
                log.error('图片保存失败，请检查网络连接', response);
                throw new Error('图片保存失败');
            }

            const result = await response.json();
            return result;
        } catch (err) {
            log.error('保存地图为图片失败:', err);
            throw err;
        }
    }

    /**
     * 清理地图实例
     */
    cleanup() {
        this.mapInstances.forEach(map => {
            if (map && map.destroy) {
                map.destroy();
            }
        });
        this.mapInstances = [];
    }

    /**
     * 获取地图实例
     */
    getMapInstance(index) {
        return this.mapInstances[index];
    }

    /**
     * 获取所有地图实例
     */
    getAllMapInstances() {
        return this.mapInstances;
    }
}

// 创建单例实例
export const mapService = new TopmeansMapService();
