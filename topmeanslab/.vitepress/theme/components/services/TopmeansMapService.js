import html2canvas from 'html2canvas';
import log from 'loglevel';

const BACKEND_SRV_URL = import.meta.env.VITE_BACKEND_SRV_URL;

/**
 * 地图服务类 - 负责高德地图相关的所有操作
 */
export class TopmeansMapService {
    constructor() {
        this.AMap = null;
        this.mapInstances = [];
        this.geocoderReady = false;
    }

    /**
     * 初始化地图服务
     */
    async initialize() {
        if (typeof window === 'undefined') return;

        let sc = '';
        let sk = '';

        try {
            const response = await fetch(`${BACKEND_SRV_URL}/api/amap_keys`, {
                method: 'GET',
                credentials: 'include'
            });
            if (!response.ok) {
                log.error('获取API密钥失败，请检查网络连接', response);
                throw new Error('获取API密钥失败，请检查网络连接');
            }

            const { AMAP_CODE, AMAP_KEY } = await response.json();
            sc = AMAP_CODE;
            sk = AMAP_KEY;
        } catch (err) {
            log.error('获取API密钥异常，请检查网络连接', err);
            throw err;
        }

        window._AMapSecurityConfig = { securityJsCode: sc };

        try {
            // 异步加载地图脚本
            this.AMap = await this.loadAMapScript(sk);

            // 预加载常用插件
            await this.loadMapPlugins();

            return this.AMap;
        } catch (err) {
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 预加载地图插件
     */
    async loadMapPlugins() {
        return new Promise((resolve) => {
            this.AMap.plugin(['AMap.Geocoder', 'AMap.AutoComplete', 'AMap.Driving'], () => {
                this.geocoderReady = true;
                log.info('地图插件加载完成');
                resolve();
            });
        });
    }

    /**
     * 设置地址自动完成功能
     */
    setupAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        // 如果插件还没加载，先加载
        if (!this.geocoderReady) {
            this.AMap.plugin(['AMap.AutoComplete'], () => {
                this.createAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect);
            });
        } else {
            this.createAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect);
        }
    }

    /**
     * 创建自动完成实例
     */
    createAutoComplete(startInputId, endInputId, onStartSelect, onEndSelect) {
        const startAuto = new this.AMap.AutoComplete({
            input: startInputId
        });

        startAuto.on("select", (e) => {
            onStartSelect(e.poi.name);
        });

        const endAuto = new this.AMap.AutoComplete({
            input: endInputId
        });

        endAuto.on("select", (e) => {
            onEndSelect(e.poi.name);
        });
    }

    /**
     * 封装地图脚本加载
     */
    loadAMapScript(key) {
        return new Promise(async (resolve, reject) => {
            if (window.AMap) return resolve(window.AMap);

            try {
                const response = await fetch(`${BACKEND_SRV_URL}/api/amap`, {
                    method: 'GET',
                    credentials: 'include'
                });
                if (!response.ok) {
                    throw new Error('地图脚本加载失败，请检查网络连接');
                }

                const { scriptUrl } = await response.json();

                const script = document.createElement('script');
                script.src = scriptUrl;
                script.onload = () => resolve(window.AMap);
                script.onerror = (err) => reject(
                    new Error('地图脚本加载失败，请检查网络连接', { cause: err })
                );
                document.head.appendChild(script);
            } catch (err) {
                reject(new Error('地图脚本加载失败，请检查网络连接', { cause: err }));
            }
        });
    }

    /**
     * 封装地理编码操作
     */
    async getGeocodePosition(geocoder, address) {
        return new Promise((resolve, reject) => {
            geocoder.getLocation(address, (status, result) => {
                if (status === 'complete' && result.geocodes.length) {
                    resolve(result.geocodes[0].location);
                } else {
                    reject(new Error(`无法解析地址: ${address}`));
                }
            });
        });
    }

    /**
     * 提取城市信息
     */
    extractCityInfo(address) {
        if (!address || typeof address !== 'string') {
            return null;
        }

        // 常见城市名称模式
        const cityPatterns = [
            /([^省]+省)?([^市]+市)/,  // 匹配省市
            /([^区]+区)/,            // 匹配区
            /(北京|上海|天津|重庆)/,   // 直辖市
            /(香港|澳门)/,           // 特别行政区
        ];

        for (const pattern of cityPatterns) {
            const match = address.match(pattern);
            if (match) {
                const city = match[0];
                log.info(`提取城市信息: "${address}" -> "${city}"`);
                return city;
            }
        }

        // 如果没有明确的城市信息，尝试从括号内容提取
        const bracketMatch = address.match(/[（(]([^）)]*[市区县])[）)]/);
        if (bracketMatch) {
            const city = bracketMatch[1];
            log.info(`从括号提取城市信息: "${address}" -> "${city}"`);
            return city;
        }

        return null;
    }

    /**
     * 地址预处理 - 提取关键地理信息
     */
    preprocessAddress(address) {
        if (!address || typeof address !== 'string') {
            return { processed: address, city: null };
        }

        const originalAddress = address;

        // 先提取城市信息
        const cityInfo = this.extractCityInfo(address);

        // 智能处理括号内容
        let processed = address;

        // 如果括号内包含城市信息，保留它
        const bracketContent = address.match(/[（(]([^）)]*)[）)]/);
        if (bracketContent) {
            const content = bracketContent[1];
            // 如果括号内容包含地理信息（市、区、县、路、街等），保留
            if (/[市区县路街道镇村]/.test(content)) {
                // 保留括号内容，只移除括号
                processed = address.replace(/[（(]/g, '').replace(/[）)]/g, '');
            } else {
                // 移除不包含地理信息的括号内容
                processed = address.replace(/[（(].*?[）)]/g, '');
            }
        }

        // 移除末尾的商业标识词，但保留地理标识词
        const commercialKeywords = ['店', '分店', '门店', '营业部', '服务中心'];
        const geoKeywords = ['路', '街', '道', '巷', '里', '村', '镇', '区', '县', '市'];

        commercialKeywords.forEach(keyword => {
            // 只有在不影响地理信息的情况下才移除
            const regex = new RegExp(keyword + '$');
            if (regex.test(processed)) {
                const temp = processed.replace(regex, '');
                // 检查移除后是否还有地理标识
                if (geoKeywords.some(geo => temp.includes(geo)) || temp.length >= 4) {
                    processed = temp;
                }
            }
        });

        // 如果处理后的地址太短，使用原地址
        if (processed.trim().length < 2) {
            processed = originalAddress;
        }

        const result = {
            processed: processed.trim(),
            city: cityInfo
        };

        log.info(`地址预处理: "${originalAddress}" -> 处理后="${result.processed}", 城市="${result.city}"`);
        return result;
    }

    /**
     * 验证坐标是否在预期城市范围内
     */
    validateLocationByCity(location, expectedCity) {
        if (!expectedCity) return true;

        // 主要城市的大致坐标范围
        const cityRanges = {
            '武汉': { lng: [113.8, 115.0], lat: [30.0, 31.0] },
            '武汉市': { lng: [113.8, 115.0], lat: [30.0, 31.0] },
            '北京': { lng: [115.7, 117.4], lat: [39.4, 41.6] },
            '北京市': { lng: [115.7, 117.4], lat: [39.4, 41.6] },
            '上海': { lng: [120.8, 122.2], lat: [30.7, 31.9] },
            '上海市': { lng: [120.8, 122.2], lat: [30.7, 31.9] },
            '广州': { lng: [112.9, 114.0], lat: [22.7, 23.9] },
            '广州市': { lng: [112.9, 114.0], lat: [22.7, 23.9] },
            '深圳': { lng: [113.7, 114.8], lat: [22.4, 22.9] },
            '深圳市': { lng: [113.7, 114.8], lat: [22.4, 22.9] },
            '杭州': { lng: [119.5, 120.9], lat: [29.8, 30.9] },
            '杭州市': { lng: [119.5, 120.9], lat: [29.8, 30.9] },
        };

        // 检查是否有匹配的城市范围
        for (const [city, range] of Object.entries(cityRanges)) {
            if (expectedCity.includes(city) || city.includes(expectedCity)) {
                const inRange = location.lng >= range.lng[0] && location.lng <= range.lng[1] &&
                               location.lat >= range.lat[0] && location.lat <= range.lat[1];
                log.info(`坐标验证: [${location.lng}, ${location.lat}] 在 ${city} 范围内: ${inRange}`);
                return inRange;
            }
        }

        return true; // 如果没有找到对应城市范围，默认通过
    }

    /**
     * 使用地理编码获取精确坐标
     */
    async geocodeAddress(address) {
        return new Promise((resolve, reject) => {
            if (!this.geocoderReady) {
                reject(new Error('地理编码插件未就绪'));
                return;
            }

            // 预处理地址，获取城市信息
            const addressInfo = this.preprocessAddress(address);
            const { processed, city } = addressInfo;

            log.info(`开始地理编码: 原地址="${address}", 处理后="${processed}", 城市="${city}"`);

            // 创建多个地理编码器，使用不同的策略
            const strategies = [];

            // 策略1: 使用城市约束的地理编码器
            if (city) {
                strategies.push({
                    name: '城市约束',
                    geocoder: new this.AMap.Geocoder({
                        city: city,
                        radius: 1000
                    }),
                    address: address
                });

                strategies.push({
                    name: '城市约束+预处理',
                    geocoder: new this.AMap.Geocoder({
                        city: city,
                        radius: 1000
                    }),
                    address: processed
                });
            }

            // 策略2: 全国范围搜索
            strategies.push({
                name: '全国范围',
                geocoder: new this.AMap.Geocoder({
                    city: "全国",
                    radius: 1000
                }),
                address: address
            });

            strategies.push({
                name: '全国范围+预处理',
                geocoder: new this.AMap.Geocoder({
                    city: "全国",
                    radius: 1000
                }),
                address: processed
            });

            // 依次尝试各种策略
            let currentStrategy = 0;

            const tryNextStrategy = () => {
                if (currentStrategy >= strategies.length) {
                    log.error(`所有地理编码策略都失败: ${address}`);
                    reject(new Error(`无法解析地址: ${address}`));
                    return;
                }

                const strategy = strategies[currentStrategy];
                log.info(`尝试策略 ${currentStrategy + 1}/${strategies.length}: ${strategy.name}, 地址="${strategy.address}"`);

                strategy.geocoder.getLocation(strategy.address, (status, result) => {
                    if (status === 'complete' && result.geocodes && result.geocodes.length > 0) {
                        const location = result.geocodes[0].location;

                        // 验证坐标是否在预期城市范围内
                        if (this.validateLocationByCity(location, city)) {
                            log.info(`地理编码成功 (${strategy.name}): ${strategy.address} -> [${location.lng}, ${location.lat}]`);
                            resolve(location);
                            return;
                        } else {
                            log.warn(`坐标不在预期城市范围内，尝试下一个策略: [${location.lng}, ${location.lat}]`);
                        }
                    } else {
                        log.warn(`策略 ${strategy.name} 失败: ${status}`);
                    }

                    currentStrategy++;
                    tryNextStrategy();
                });
            };

            tryNextStrategy();
        });
    }

    /**
     * 初始化地图实例
     */
    async initMap(index, start, end) {
        if (!this.AMap) {
            throw new Error('地图服务未初始化');
        }

        try {
            const map = new this.AMap.Map(`map-container-${index}`, {
                renderer: "canvas",
                resizeEnable: true,
                viewMode: "2D",
                crossOrigin: 'anonymous',
                WebGLParams: {
                    preserveDrawingBuffer: true
                }
            });

            this.mapInstances.push(map);

            // 先进行地理编码，获取精确坐标
            log.info(`开始地理编码: 起点="${start}", 终点="${end}"`);

            const [startLocation, endLocation] = await Promise.all([
                this.geocodeAddress(start),
                this.geocodeAddress(end)
            ]);

            // 构造路线导航类
            const driving = new this.AMap.Driving({
                map: map,
                panel: "",  // 指定空字符串禁用默认面板
                renderer: "canvas", // 使用Canvas绘制路线
            });

            // 使用精确坐标进行路线规划
            driving.search(startLocation, endLocation, function (status, result) {
                if (status === 'complete') {
                    log.info('路线规划成功');
                    if (result.routes && result.routes.length > 0) {
                        log.info(`路线信息: 距离=${result.routes[0].distance}米, 时间=${result.routes[0].time}秒`);
                    }
                } else {
                    log.error('获取驾车数据失败：' + result);
                }
            });

            return map;
        } catch (err) {
            log.error('地图初始化失败:', err);
            throw new Error(`地图初始化失败: ${err.message}`);
        }
    }

    /**
     * 驾车路线规划
     */
    async drivingPlanning(index, start, end) {
        try {
            const container = document.getElementById(`map-container-${index}`);
            if (!container) {
                log.error('地图容器未找到');
                throw new Error('地图容器未找到');
            }

            await this.initMap(index, start, end);
        } catch (err) {
            log.error('路线生成错误', err);
            throw new Error(err.message || '路线生成失败');
        }
    }

    /**
     * 保存地图为图片
     */
    async saveMapAsImage(index, account, formattedDateTime) {
        try {
            const mapContainer = document.getElementById(`map-container-${index}`);
            if (!mapContainer) {
                throw new Error('地图容器未找到');
            }

            // 使用 html2canvas 截取地图容器
            const canvas = await html2canvas(mapContainer, {
                useCORS: true,        // 启用跨域
                allowTaint: true,     // 允许污染模式
                logging: true,        // 开启日志查看问题
                scale: 2,             // 提高分辨率
                ignoreElements: (element) => {
                    // 忽略版权信息外的其他遮挡元素
                    return element.id === 'some-obstructive-element';
                },
            });

            // 将 Canvas 转换为 Base64 数据
            const imageData = canvas.toDataURL('image/png');

            // 发送图片数据到后端
            const response = await fetch(`${BACKEND_SRV_URL}/api/save_amap_img`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: imageData, // Base64 图片数据
                    user: account,
                    filename: `map-${formattedDateTime}-${index}.png` // 文件名
                })
            });

            if (!response.ok) {
                log.error('图片保存失败，请检查网络连接', response);
                throw new Error('图片保存失败');
            }

            const result = await response.json();
            return result;
        } catch (err) {
            log.error('保存地图为图片失败:', err);
            throw err;
        }
    }

    /**
     * 清理地图实例
     */
    cleanup() {
        this.mapInstances.forEach(map => {
            if (map && map.destroy) {
                map.destroy();
            }
        });
        this.mapInstances = [];
    }

    /**
     * 获取地图实例
     */
    getMapInstance(index) {
        return this.mapInstances[index];
    }

    /**
     * 获取所有地图实例
     */
    getAllMapInstances() {
        return this.mapInstances;
    }
}

// 创建单例实例
export const mapService = new TopmeansMapService();
