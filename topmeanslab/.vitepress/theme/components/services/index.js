/**
 * 服务模块统一导出
 * 提供所有Topmeans相关服务的统一入口
 */

// 导入所有服务
export { mapService, TopmeansMapService } from './TopmeansMapService.js';
export { scrollManager, TopmeansScrollManager } from './TopmeansScrollManager.js';
export { formManager, TopmeansFormManager } from './TopmeansFormManager.js';
export { apiService, TopmeansApiService } from './TopmeansApiService.js';
export { styleManager, TopmeansStyleManager } from './TopmeansStyleManager.js';

/**
 * 服务管理器 - 统一管理所有服务的生命周期
 */
export class TopmeansServiceManager {
    constructor() {
        this.services = {
            map: null,
            scroll: null,
            form: null,
            api: null,
            style: null
        };
        this.initialized = false;
    }

    /**
     * 初始化所有服务
     */
    async initialize() {
        if (this.initialized) return;

        try {
            // 导入服务实例
            const { mapService } = await import('./TopmeansMapService.js');
            const { scrollManager } = await import('./TopmeansScrollManager.js');
            const { formManager } = await import('./TopmeansFormManager.js');
            const { apiService } = await import('./TopmeansApiService.js');
            const { styleManager } = await import('./TopmeansStyleManager.js');

            this.services.map = mapService;
            this.services.scroll = scrollManager;
            this.services.form = formManager;
            this.services.api = apiService;
            this.services.style = styleManager;

            this.initialized = true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取地图服务
     */
    getMapService() {
        return this.services.map;
    }

    /**
     * 获取滚动管理器
     */
    getScrollManager() {
        return this.services.scroll;
    }

    /**
     * 获取表单管理器
     */
    getFormManager() {
        return this.services.form;
    }

    /**
     * 获取API服务
     */
    getApiService() {
        return this.services.api;
    }

    /**
     * 获取样式管理器
     */
    getStyleManager() {
        return this.services.style;
    }

    /**
     * 清理所有服务
     */
    cleanup() {
        Object.values(this.services).forEach(service => {
            if (service && typeof service.cleanup === 'function') {
                service.cleanup();
            }
        });

        this.services = {
            map: null,
            scroll: null,
            form: null,
            api: null,
            style: null
        };

        this.initialized = false;
    }

    /**
     * 重新初始化所有服务
     */
    async reinitialize() {
        this.cleanup();
        await this.initialize();
    }

    /**
     * 检查服务是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 获取所有服务的状态
     */
    getServicesStatus() {
        return {
            initialized: this.initialized,
            services: {
                map: !!this.services.map,
                scroll: !!this.services.scroll,
                form: !!this.services.form,
                api: !!this.services.api,
                style: !!this.services.style
            }
        };
    }
}

// 创建服务管理器单例
export const serviceManager = new TopmeansServiceManager();

/**
 * 便捷的初始化函数
 */
export async function initializeTopmeansServices() {
    await serviceManager.initialize();
    return serviceManager;
}

/**
 * 便捷的清理函数
 */
export function cleanupTopmeansServices() {
    serviceManager.cleanup();
}

/**
 * 获取所有服务实例的便捷函数
 */
export function getTopmeansServices() {
    if (!serviceManager.isInitialized()) {
        throw new Error('服务管理器未初始化，请先调用 initializeTopmeansServices()');
    }

    return {
        mapService: serviceManager.getMapService(),
        scrollManager: serviceManager.getScrollManager(),
        formManager: serviceManager.getFormManager(),
        apiService: serviceManager.getApiService(),
        styleManager: serviceManager.getStyleManager()
    };
}
