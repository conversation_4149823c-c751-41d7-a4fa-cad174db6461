import { h } from 'vue'
import DefaultTheme from 'vitepress/theme'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createPinia } from 'pinia'
import Layout from './Layout.vue'

export default {
  extends: DefaultTheme,
  Layout: () => {
    return h(Layout, null, {
      'home-features-before': () => h('div', null, ''),
      'home-features-after': () => h('div', null, ''),
      'home-hero-before': () => h('div', null, ''),
      'home-hero-after': () => h('div', null, ''),
      'home-footer-before': () => h('div', null, ''),
      'doc-before': () => h('div', null, ''),
      'doc-after': () => h('div', null, ''),
      'aside-top': () => h('div', null, ''),
      'aside-bottom': () => h('div', null, ''),
      'aside-outline-before': () => h('div', null, ''),
      'aside-outline-after': () => h('div', null, ''),
      'aside-ads-before': () => h('div', null, ''),
      'aside-ads-after': () => h('div', null, ''),
    })
  },
  enhanceApp({ app }) {
    const pinia = createPinia()
    app.use(pinia)
    app.use(ElementPlus)

    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
  },
} 