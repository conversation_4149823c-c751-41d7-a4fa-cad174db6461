import { defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  title: "TopMeansLab",
  description: "TopMeansLab Documentation",
  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
    ['link', { rel: 'shortcut icon', href: '/favicon.ico' }],
    ['link', { rel: 'apple-touch-icon', href: '/favicon.ico' }]
  ],
  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: '首页', link: '/' },
      { text: '用户中心', link: '/user-center/login' },
      { text: '服务购买', link: '/service-purchase/' },
      { text: 'ShowCase', link: '/showcase/' }
    ],

    sidebar: {
      '/user-center/': [
        {
          text: '用户中心',
          items: [
            { text: '登录/注册', link: '/user-center/login' },
            { text: '个人主页', link: '/user-center/profile' }
          ]
        }
      ],
      '/service-purchase/': [
        {
          text: '服务购买',
          items: [
            { text: '套餐选择', link: '/service-purchase/' }
          ]
        }
      ],
      '/showcase/': [
        {
          text: 'ShowCase',
          items: [
            { text: 'Tour Planning Examples', link: '/showcase/' }
          ]
        }
      ]
    }
  },
  vite: {
    ssr: {
      noExternal: ['element-plus']
    },
    server: {
      host: '0.0.0.0',  // 允许所有IP访问
      port: 5173,       // 指定端口
      strictPort: true, // 端口被占用时直接退出
      hmr: {
        host: 'localhost' // 热更新主机
      }
    }
  }
})
