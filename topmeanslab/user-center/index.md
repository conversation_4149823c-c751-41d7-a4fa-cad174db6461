---
layout: page
title: 用户中心
---

<script setup>
import { useUserStore } from '../.vitepress/theme/components/UserCenter/userStore'
import UserProfile from '../.vitepress/theme/components/UserCenter/UserProfile.vue'
import UserCenter from '../.vitepress/theme/components/UserCenter/UserCenter.vue'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { isLoggedIn } = storeToRefs(userStore)
</script>

<template>
  <div>
    <UserCenter v-if="!isLoggedIn" />
    <UserProfile v-else />
  </div>
</template> 