require('dotenv').config();
const { createLogger, format, transports } = require('winston');
const { combine, timestamp, printf, colorize } = format;
const DailyRotateFile = require('winston-daily-rotate-file');

// 自定义日志格式
const logFormat = printf(({ level, message, timestamp }) => {
    return `${timestamp} [${level}]: ${message}`;
});

// 创建日志记录器
const logger = createLogger({
    level: 'info', // 默认日志级别
    format: combine(
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
    ),
    transports: [
        // 控制台输出
        new transports.Console({
            format: combine(
                colorize(), // 彩色输出
                logFormat
            )
        }),
        // 按日期分割日志文件
        new DailyRotateFile({
            filename: 'logs/application-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '20m', // 单个日志文件最大 20MB
            maxFiles: '14d' // 保留最近 14 天的日志
        })
    ]
});

module.exports = logger;